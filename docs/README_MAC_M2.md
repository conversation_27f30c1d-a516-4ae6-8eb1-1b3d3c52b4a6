# Mac M2/M3 Apple Silicon 使用指南

## 🍎 专为 Mac M2/M3 优化的 Qwen3-4B-Thinking-2507 医学LLM微调

### ⚡️ 快速开始

```bash
# 1. 运行自动设置脚本（会创建虚拟环境）
./setup_qwen3.sh

# 2. 激活虚拟环境（重要！）
source venv/bin/activate

# 3. 运行 Mac M2 专用检查脚本
python run_mac_m2.py

# 4. 开始训练
python medical_llm_trainer.py
```

### 🔧 Mac M2/M3 特殊优化

#### 1. 虚拟环境管理
- **自动创建**: 脚本会自动创建 `venv` 虚拟环境
- **避免冲突**: 解决 `externally-managed-environment` 错误
- **记得激活**: 每次使用前运行 `source venv/bin/activate`

#### 2. Apple Silicon MPS 加速
```python
# 自动检测和使用 MPS
if torch.backends.mps.is_available():
    device = "mps"  # Apple Silicon GPU 加速
```

#### 3. 内存优化配置
```yaml
# config_mac_m2.yaml - 专门为 Mac M2 优化
training:
  parameters:
    per_device_train_batch_size: 1    # 小批次
    gradient_accumulation_steps: 8    # 补偿小批次
    max_length: 1024                  # 降低序列长度
  quantization:
    load_in_4bit: false              # 关闭量化避免问题
```

### 📊 性能对比

| 配置项 | 标准设置 | Mac M2 优化 | 说明 |
|--------|----------|-------------|------|
| Batch Size | 2-4 | 1 | 避免内存溢出 |
| 序列长度 | 2048 | 1024 | 节省内存 |
| 量化 | 4-bit | 关闭 | MPS兼容性 |
| 梯度累积 | 4 | 8 | 补偿小批次 |
| 设备 | CUDA | MPS | Apple Silicon |

### 🚀 推荐工作流程

#### 1. 环境准备
```bash
# 克隆项目
git clone <your-repo>
cd medllm-finetune-rag

# 运行Mac M2专用安装
./setup_qwen3.sh
```

#### 2. 验证环境
```bash
# 激活环境
source venv/bin/activate

# 运行系统检查
python run_mac_m2.py
```

#### 3. 数据准备
```bash
# 处理数据
python data_processing.py

# 检查处理结果
ls processed_data/
```

#### 4. 开始训练
```bash
# 使用Mac M2优化配置
python medical_llm_trainer.py
```

### ⚠️ 常见问题解决

#### 1. `externally-managed-environment` 错误
```bash
# 解决方案：使用虚拟环境
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 2. MPS 内存不足
```python
# 降低配置
config = {
    "per_device_train_batch_size": 1,
    "max_seq_length": 512,
    "gradient_accumulation_steps": 16
}
```

#### 3. Unsloth 安装失败
```bash
# 不用担心，会自动降级到标准训练
# 性能稍慢但功能完整
```

#### 4. 模型加载缓慢
```bash
# 设置缓存目录
export HF_HOME="./hf_cache"
export TRANSFORMERS_CACHE="./transformers_cache"
```

### 💡 性能优化技巧

#### 1. 内存管理
```python
# 启用内存优化
import torch
torch.backends.mps.empty_cache()  # 清理MPS缓存

# 监控内存使用
import psutil
memory = psutil.virtual_memory()
print(f"内存使用: {memory.percent}%")
```

#### 2. 训练策略
- **小批次训练**: batch_size=1, gradient_accumulation=8
- **检查点保存**: 每100步保存一次
- **混合精度**: 使用 fp16 而不是 bf16
- **序列长度**: 从512开始，逐步增加

#### 3. 数据优化
```python
# 限制训练数据量（测试用）
max_samples = 1000  # 从小数据集开始

# 数据预处理优化
num_workers = 2     # 降低并发数
pin_memory = False  # MPS不需要
```

### 📈 监控和调试

#### 1. 实时监控
```bash
# 监控系统资源
htop

# 监控GPU使用（如果有独立显卡）
nvidia-smi

# 监控MPS
sudo powermetrics -s gpu_power -n 1
```

#### 2. 训练日志
```python
# W&B 集成
import wandb
wandb.init(
    project="medical-llm-mac-m2",
    tags=["apple-silicon", "qwen3", "thinking"]
)
```

### 🎯 最佳实践

1. **渐进式训练**
   - 从小数据集开始
   - 逐步增加复杂度
   - 验证每个步骤

2. **资源管理**
   - 关闭不必要的应用
   - 监控内存使用
   - 定期清理缓存

3. **实验记录**
   - 记录配置参数
   - 保存训练日志
   - 对比不同设置

### 📞 获取帮助

如果遇到问题：

1. **检查日志**: 查看训练输出和错误信息
2. **系统检查**: 运行 `python run_mac_m2.py`
3. **重置环境**: 删除 `venv` 文件夹重新安装
4. **降级配置**: 使用更小的模型或数据集

---

**Happy Training on Apple Silicon! 🚀**
