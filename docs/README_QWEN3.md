# Qwen3-4B-Thinking-2507 医学大语言模型微调

基于 [Unsloth](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune/qwen3-2507#fine-tuning-qwen3-2507-with-unsloth) 官方实现的 Qwen3-4B-Thinking-2507 医学实体关系抽取模型微调系统。

## 🌟 主要特性

- ✅ **Qwen3-4B-Thinking-2507**: 使用最新的思考模式模型
- ✅ **Unsloth 加速**: 2x 更快训练，70% 更少 VRAM 使用
- ✅ **思考模式支持**: 支持 `<think></think>` 标签的推理模式
- ✅ **官方模式**: 完全基于 Unsloth 官方 notebook 实现
- ✅ **医学优化**: 专门针对医学实体关系抽取优化
- ✅ **自动优化**: 使用 `train_on_responses_only` 提升训练效果

## 📋 系统要求

- Python 3.8+
- CUDA 11.8+ (推荐)
- 8GB+ GPU 内存 (4B 模型，使用 QLoRA)
- 16GB+ 系统内存

## 🚀 快速开始

### 1. 环境设置

```bash
# 克隆项目
git clone <your-repo-url>
cd medllm-finetune-rag

# 运行自动设置脚本
./setup_qwen3.sh
```

### 2. 运行示例

```bash
# 运行基础示例
python3 qwen3_example.py
```

### 3. 开始训练

```bash
# 准备数据后运行完整训练
python3 medical_llm_trainer.py
```

## 🔧 配置说明

### 模型配置 (`config.yaml`)

```yaml
model:
  name: "Qwen/Qwen3-4B-Thinking-2507"  # 使用官方模型名
  cache_dir: "./model_cache"
  trust_remote_code: true
  torch_dtype: "bfloat16"

training:
  lora:
    r: 32          # 官方推荐值
    alpha: 32      # 匹配 r 值
    dropout: 0     # 官方优化设置
```

### 主要更新

#### 1. 使用 FastModel 替代 FastLanguageModel
```python
from unsloth import FastModel  # 新的 API

model, tokenizer = FastModel.from_pretrained(
    model_name="unsloth/Qwen3-4B-Thinking-2507",
    max_seq_length=2048,
    load_in_4bit=True,
    full_finetuning=False
)
```

#### 2. 官方 Chat Template
```python
from unsloth.chat_templates import get_chat_template

tokenizer = get_chat_template(
    tokenizer,
    chat_template="qwen3-thinking"  # 官方模板
)
```

#### 3. SFTConfig 训练配置
```python
from trl import SFTTrainer, SFTConfig

trainer = SFTTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=dataset,
    args=SFTConfig(
        dataset_text_field="text",
        per_device_train_batch_size=2,
        gradient_accumulation_steps=4,
        learning_rate=2e-4,
        optim="adamw_8bit",  # 官方推荐
        # ... 其他参数
    )
)
```

#### 4. 响应训练优化
```python
from unsloth.chat_templates import train_on_responses_only

trainer = train_on_responses_only(
    trainer,
    instruction_part="<|im_start|>user\n",
    response_part="<|im_start|>assistant\n"
)
```

## 💡 思考模式使用

### 训练数据格式
```json
{
  "instruction": "从医学文献中抽取实体和关系",
  "input": "Helicobacter pylori causes gastric cancer...",
  "output": "<think>\n我需要分析这段文本...\n</think>\n\n{\"entities\": [...], \"relations\": [...]}"
}
```

### 推理模式
```python
# 启用思考模式 (推荐用于复杂任务)
result = trainer.inference(text, enable_thinking=True)

# 禁用思考模式 (更快的直接回答)
result = trainer.inference(text, enable_thinking=False)
```

## 📊 性能对比

| 特性 | 传统方法 | Unsloth + Qwen3 |
|------|----------|------------------|
| 训练速度 | 基准 | 2x 更快 |
| 内存使用 | 基准 | 70% 更少 |
| 推理质量 | 标准 | 思考模式增强 |
| 上下文长度 | 2048 | 最高 262K |

## 🔍 生成参数

### 思考模式参数 (复杂推理)
```python
generation_params = {
    "temperature": 0.6,
    "top_p": 0.95,
    "top_k": 20,
    "max_new_tokens": 1024
}
```

### 直接模式参数 (快速回答)
```python
generation_params = {
    "temperature": 0.7,
    "top_p": 0.8,
    "top_k": 20,
    "max_new_tokens": 512
}
```

## 📁 项目结构

```
medllm-finetune-rag/
├── medical_llm_trainer.py      # 主训练器 (已更新为Qwen3)
├── config.yaml                 # 配置文件 (已更新)
├── requirements.txt            # 依赖列表 (已添加unsloth)
├── qwen3_example.py           # 使用示例
├── setup_qwen3.sh            # 环境设置脚本
├── README_QWEN3.md           # 本文档
└── qwen3_(4b)_thinking.py    # 官方参考notebook
```

## 🐛 常见问题

### 1. Unsloth 安装失败
```bash
# 如果在 Colab 环境
pip install --no-deps bitsandbytes accelerate xformers peft trl unsloth_zoo
pip install --no-deps unsloth

# 如果在本地环境
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

### 2. 模型加载错误
确保使用正确的模型名称：
- 使用 Unsloth: `"unsloth/Qwen3-4B-Thinking-2507"`
- 使用 HuggingFace: `"Qwen/Qwen3-4B-Thinking-2507"`

### 3. CUDA 内存不足
- 减少 `per_device_train_batch_size`
- 增加 `gradient_accumulation_steps`
- 使用更小的 `max_seq_length`

## 📚 参考资源

- [Unsloth Qwen3 官方文档](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune/qwen3-2507#fine-tuning-qwen3-2507-with-unsloth)
- [Qwen3-4B-Thinking-2507 模型页面](https://huggingface.co/Qwen/Qwen3-4B-Thinking-2507)
- [Unsloth GitHub](https://github.com/unslothai/unsloth)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目遵循 MIT 许可证。
