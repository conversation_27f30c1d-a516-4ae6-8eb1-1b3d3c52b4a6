# MedLLM PyPI Package Guide

## 📦 Package Structure Analysis

这个项目已经被设计成一个完整的PyPI包，以下是需要抽象出来的关键组件：

### 🏗️ 核心抽象组件

#### 1. **主要API类**
```python
# 核心训练类
from medllm import MedicalLLMTrainer, MedicalDataProcessor, MedicalRAGSystem

# 评估工具
from medllm import EntityEvaluator, RelationEvaluator, MedicalNERREEvaluator

# 配置管理
from medllm import load_config, create_config_for_device

# 高级API
from medllm import quick_train, quick_evaluate, quick_inference
```

#### 2. **命令行接口**
```bash
# 核心命令
medllm info                    # 系统信息
medllm train data.json         # 快速训练
medllm eval model test.json    # 模型评估
medllm infer model "text"      # 推理

# 项目管理
medllm setup my_project        # 项目初始化
medllm config --device cuda    # 配置生成
```

#### 3. **配置抽象**
- **设备特定配置**: CUDA, MPS, CPU自动优化
- **训练模板**: quick, full, research, production
- **自动检测**: 硬件和依赖项自动配置

#### 4. **数据处理抽象**
```python
# 数据结构
from medllm import MedicalRecord, Entity, Relation

# 数据处理
processor = MedicalDataProcessor()
processed_data = processor.load_and_process("data.json")
```

### 🎯 关键抽象设计原则

#### 1. **简单性优先**
```python
# 一行训练
trainer = quick_train("medical_data.json")

# 一行评估
results = quick_evaluate("./model", "test_data.json")
```

#### 2. **配置驱动**
```python
# 使用配置文件
trainer = create_trainer(config="config.yaml")

# 自动配置
trainer = create_trainer()  # 自动检测最佳配置
```

#### 3. **平台适配**
- **Mac M2/MPS**: 自动优化批量大小和内存使用
- **CUDA**: QLoRA和高效并行
- **CPU**: 内存友好的回退模式

#### 4. **渐进式复杂性**
```python
# 级别1: 快速开始
quick_train("data.json")

# 级别2: 配置定制  
create_trainer(config="my_config.yaml")

# 级别3: 完全控制
trainer = MedicalLLMTrainer(
    model_name="Qwen/Qwen3-4B-Thinking-2507",
    lora_config={...},
    training_args={...}
)
```

### 📋 包安装选项

#### 基础安装
```bash
pip install medllm-finetune-rag
```

#### 功能特定安装
```bash
# Unsloth加速
pip install medllm-finetune-rag[unsloth]

# RAG功能
pip install medllm-finetune-rag[rag]

# GPU支持
pip install medllm-finetune-rag[gpu]

# 完整功能
pip install medllm-finetune-rag[all]
```

### 🔧 核心工具类

#### 1. **UniversalMedicalTrainer**
- 配置驱动的通用训练器
- 支持多种模型和训练策略
- 自动设备检测和优化

#### 2. **MedicalRAGSystem**
- RAG-Anything集成
- 多模态处理（图像、表格、公式）
- 知识库构建和查询

#### 3. **HuggingFaceUploader**
- 模型上传到HF Hub
- 自动生成模型卡片
- 版本管理

### 🎨 用户体验设计

#### 1. **零配置开始**
```python
import medllm

# 自动检测设备和配置
trainer = medllm.quick_train("medical_data.json")
```

#### 2. **渐进式定制**
```python
# 使用模板
config = medllm.get_config_template("research")

# 设备优化
config = medllm.create_config_for_device("cuda")

# 完全自定义
config = medllm.load_config("my_config.yaml")
```

#### 3. **丰富的CLI**
```bash
# 项目初始化
medllm setup my_medical_project --device auto

# 数据验证
medllm data validate training_data.json

# 配置生成
medllm config --template research --device cuda

# RAG系统
medllm rag --setup --build knowledge_base.json
```

### 📊 包的优势

#### 1. **开发效率**
- 一行代码开始训练
- 自动配置和优化
- 丰富的模板和预设

#### 2. **灵活性**
- 支持多种模型架构
- 可配置的训练策略
- 扩展友好的API

#### 3. **生产就绪**
- 完整的CLI工具
- 配置管理
- 错误处理和日志

#### 4. **社区友好**
- 清晰的文档
- 示例和教程
- 测试覆盖

### 🚀 发布计划

#### 版本 0.1.0 (当前)
- ✅ 核心训练功能
- ✅ RAG-Anything集成
- ✅ CLI接口
- ✅ 多平台支持

#### 版本 0.2.0 (计划)
- 🔄 更多模型支持
- 🔄 增强的RAG功能
- 🔄 Web界面
- 🔄 分布式训练

#### 版本 1.0.0 (目标)
- 🎯 稳定的API
- 🎯 完整文档
- 🎯 性能优化
- 🎯 企业级功能

### 📝 使用示例

#### 快速开始
```python
from medllm import quick_train, quick_evaluate

# 训练模型
trainer = quick_train("medical_data.json")

# 评估模型
results = quick_evaluate("./medllm_output", "test_data.json")
print(f"F1 Score: {results['entity_f1']:.3f}")
```

#### 高级使用
```python
from medllm import create_trainer, create_rag_system

# 创建配置优化的训练器
trainer = create_trainer(
    model_name="Qwen/Qwen3-4B-Thinking-2507",
    config="research_config.yaml"
)

# 训练模型
trainer.train("medical_data.json")

# 创建RAG系统
rag = create_rag_system(working_dir="./rag_workspace")
await rag.build_knowledge_base_from_json("medical_literature.json")

# RAG增强推理
response = await rag.generate_response("What are the symptoms of diabetes?")
```

### 🏆 总结

这个PyPI包抽象出了医学LLM微调的所有复杂性，提供了：

1. **简单的高级API** - 适合快速原型
2. **灵活的中级API** - 适合定制需求  
3. **完整的低级API** - 适合高级用户
4. **强大的CLI工具** - 适合日常使用
5. **自动化配置** - 适合生产环境

用户可以从简单的一行代码开始，随着需求增长逐步使用更高级的功能。
