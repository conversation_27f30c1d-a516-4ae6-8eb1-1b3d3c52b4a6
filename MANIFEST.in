# Include package data
include medllm/configs/*.yaml
include medllm/templates/*.txt
include medllm/data/*.json

# Include documentation
include README.md
include LICENSE
include CHANGELOG.md

# Include requirements
include requirements.txt
include pyproject.toml

# Include examples
recursive-include examples *.py
recursive-include examples *.json
recursive-include examples *.md

# Exclude unwanted files
exclude *.pyc
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * .DS_Store
exclude .env
exclude .gitignore
exclude *.log

# Exclude development files
exclude scripts/*
exclude tests/*
exclude docs/*.md
exclude venv/*
exclude model_cache/*
exclude medical_llm_output/*
exclude vector_db/*
exclude logs/*
exclude evaluation_results/*
