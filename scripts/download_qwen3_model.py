#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3-4B-Thinking-2507 模型下载脚本
支持从 ModelScope 和 HuggingFace 下载
"""

import os
import sys
import argparse
from pathlib import Path

def download_from_modelscope(model_name, cache_dir):
    """使用 ModelScope 下载模型"""
    try:
        from modelscope import snapshot_download
        print(f"使用 ModelScope 下载模型: {model_name}")
        
        model_dir = snapshot_download(
            model_name,
            cache_dir=cache_dir,
            revision='master'
        )
        print(f"模型下载完成，保存在: {model_dir}")
        return model_dir
    except ImportError:
        print("错误: 未安装 modelscope，请先安装: pip install modelscope")
        return None
    except Exception as e:
        print(f"ModelScope 下载失败: {e}")
        return None

def download_from_huggingface(model_name, cache_dir):
    """使用 HuggingFace 下载模型"""
    try:
        from huggingface_hub import snapshot_download
        print(f"使用 HuggingFace 下载模型: {model_name}")
        
        model_dir = snapshot_download(
            repo_id=model_name,
            cache_dir=cache_dir,
            revision='main'
        )
        print(f"模型下载完成，保存在: {model_dir}")
        return model_dir
    except ImportError:
        print("错误: 未安装 huggingface_hub，请先安装: pip install huggingface_hub")
        return None
    except Exception as e:
        print(f"HuggingFace 下载失败: {e}")
        return None

def check_model_exists(cache_dir, model_name):
    """检查模型是否已经存在"""
    cache_path = Path(cache_dir)
    if cache_path.exists():
        # 检查是否有模型文件
        for item in cache_path.rglob("*"):
            if item.is_file() and (item.name.endswith('.bin') or 
                                 item.name.endswith('.safetensors') or
                                 item.name == 'config.json'):
                print(f"发现已存在的模型文件: {item}")
                return True
    return False

def main():
    parser = argparse.ArgumentParser(description='下载 Qwen3-4B-Thinking-2507 模型')
    parser.add_argument('--model-name', default='qwen/Qwen3-4B-Thinking-2507',
                       help='ModelScope 模型名称 (默认: qwen/Qwen3-4B-Thinking-2507)')
    parser.add_argument('--hf-model-name', default='Qwen/Qwen3-4B-Thinking-2507',
                       help='HuggingFace 模型名称 (默认: Qwen/Qwen3-4B-Thinking-2507)')
    parser.add_argument('--cache-dir', default='./model_cache',
                       help='模型缓存目录 (默认: ./model_cache)')
    parser.add_argument('--source', choices=['modelscope', 'huggingface', 'auto'],
                       default='auto', help='下载源 (默认: auto)')
    parser.add_argument('--force', action='store_true',
                       help='强制重新下载，即使模型已存在')
    
    args = parser.parse_args()
    
    # 创建缓存目录
    cache_dir = Path(args.cache_dir)
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    print("=== Qwen3-4B-Thinking-2507 模型下载工具 ===")
    print(f"缓存目录: {cache_dir.absolute()}")
    
    # 检查模型是否已存在
    if not args.force and check_model_exists(cache_dir, args.model_name):
        print("模型已存在，跳过下载。使用 --force 参数强制重新下载。")
        return
    
    model_dir = None
    
    if args.source == 'modelscope':
        model_dir = download_from_modelscope(args.model_name, str(cache_dir))
    elif args.source == 'huggingface':
        model_dir = download_from_huggingface(args.hf_model_name, str(cache_dir))
    else:  # auto
        # 优先尝试 ModelScope（在中国网络环境下更稳定）
        print("自动选择下载源...")
        model_dir = download_from_modelscope(args.model_name, str(cache_dir))
        if model_dir is None:
            print("ModelScope 下载失败，尝试 HuggingFace...")
            model_dir = download_from_huggingface(args.hf_model_name, str(cache_dir))
    
    if model_dir:
        print(f"\n✅ 模型下载成功！")
        print(f"模型路径: {model_dir}")
        
        # 验证关键文件
        model_path = Path(model_dir)
        config_file = model_path / "config.json"
        if config_file.exists():
            print(f"✅ 配置文件存在: {config_file}")
        else:
            print("⚠️  警告: 未找到 config.json 文件")
            
        # 查找模型权重文件
        weight_files = list(model_path.glob("*.bin")) + list(model_path.glob("*.safetensors"))
        if weight_files:
            print(f"✅ 找到 {len(weight_files)} 个权重文件")
            for f in weight_files[:3]:  # 只显示前3个
                print(f"   - {f.name}")
            if len(weight_files) > 3:
                print(f"   - ... 还有 {len(weight_files) - 3} 个文件")
        else:
            print("⚠️  警告: 未找到权重文件")
    else:
        print("❌ 模型下载失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
