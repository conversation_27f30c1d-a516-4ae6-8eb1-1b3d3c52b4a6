#!/bin/bash
# Qwen3-4B-Thinking-2507 医学LLM微调环境设置脚本 (Mac M2 优化版)

echo "=== 设置Qwen3-4B-Thinking-2507医学LLM微调环境 (Mac M2) ==="
echo

# 检查Python版本
echo "1. 检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误: 请确保安装了Python 3.8+"
    exit 1
fi

# 检查是否已经在虚拟环境中
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "   ✓ 检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "   创建和激活虚拟环境..."
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        echo "   ✓ 虚拟环境创建完成"
    else
        echo "   ✓ 虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    echo "   ✓ 虚拟环境已激活"
fi

# 升级pip
echo "2. 升级pip..."
python -m pip install --upgrade pip

# 检测运行环境和架构
echo "3. 检测运行环境和架构..."
ARCH=$(uname -m)
echo "   系统架构: $ARCH"

if [[ -n "${COLAB_GPU}" ]]; then
    echo "   检测到Google Colab环境"
    echo "   安装Colab专用依赖..."
    pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo
    pip install sentencepiece protobuf "datasets>=3.4.1,<4.0.0" "huggingface_hub>=0.34.0" hf_transfer
    pip install --no-deps unsloth
elif [[ "$ARCH" == "arm64" ]]; then
    echo "   检测到 Apple Silicon (M1/M2/M3) 环境"
    echo "   安装 Apple Silicon 优化依赖..."
    
    # 安装基础依赖
    pip install torch torchvision torchaudio
    pip install "transformers>=4.35.0"
    pip install "accelerate>=0.24.0"
    pip install "datasets>=2.14.0"
    pip install "tokenizers>=0.14.0"
    
    # 安装训练相关依赖
    pip install "peft>=0.6.0"
    pip install "trl>=0.7.0"
    pip install "bitsandbytes>=0.41.0"
    
    # 尝试安装 Unsloth (可能需要从源码编译)
    echo "   安装 Unsloth (Apple Silicon)..."
    pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git" || {
        echo "   ⚠️  Unsloth 安装失败，将使用标准 transformers 训练"
        echo "   这不会影响模型训练，只是会稍微慢一些"
    }
    
else
    echo "   检测到 x86_64 环境"
    echo "   安装标准依赖..."
    pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
fi

# 安装其他必需依赖
echo "4. 安装其他必需依赖..."
pip install "scikit-learn>=1.3.0"
pip install "numpy>=1.24.0"
pip install "pandas>=2.0.0"
pip install "wandb>=0.16.0"
pip install "sentence-transformers>=2.2.0"
pip install "tqdm>=4.65.0"

# 安装RAG-Anything依赖
echo "5. 安装RAG-Anything依赖..."
pip install 'raganything[all]'
pip install "python-dotenv>=1.0.0"
pip install "huggingface_hub>=0.20.0"
pip install "PyYAML>=6.0"
echo "   ✓ RAG-Anything安装完成"

# 验证安装
echo "6. 验证安装..."
python -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import torch
    print(f'✓ PyTorch {torch.__version__} 安装成功')
    
    # 检查 Apple Silicon MPS 支持
    if torch.backends.mps.is_available():
        print('✓ Apple Silicon MPS 加速可用')
        device = 'mps'
    elif torch.cuda.is_available():
        print(f'✓ CUDA可用: {torch.cuda.get_device_name(0)}')
        device = 'cuda'
    else:
        print('⚠️ 使用CPU训练 (推荐使用GPU以获得更好性能)')
        device = 'cpu'
    
    print(f'推荐训练设备: {device}')
        
except ImportError:
    print('✗ PyTorch安装失败')
    sys.exit(1)

try:
    import transformers
    print(f'✓ Transformers {transformers.__version__} 安装成功')
except ImportError:
    print('✗ Transformers安装失败')
    sys.exit(1)

try:
    from unsloth import FastModel
    from unsloth.chat_templates import get_chat_template
    from trl import SFTTrainer, SFTConfig
    print('✓ Unsloth安装成功 - 将使用加速训练')
    unsloth_available = True
except ImportError as e:
    print(f'⚠️ Unsloth不可用: {e}')
    print('  将使用标准transformers训练 (稍慢但功能完整)')
    unsloth_available = False

try:
    import peft
    print(f'✓ PEFT {peft.__version__} 安装成功')
except ImportError:
    print('✗ PEFT安装失败')
    sys.exit(1)

try:
    from raganything import RAGAnything
    from lightrag import LightRAG
    print('✓ RAG-Anything组件安装成功')
except ImportError as e:
    print(f'⚠️ RAG-Anything组件不完整: {e}')
    print('  基础训练功能仍可使用')

print(f'\\n训练配置建议:')
if unsloth_available:
    print('- 使用 use_unsloth=True 获得最佳性能')
else:
    print('- 使用 use_unsloth=False 进行标准训练')
    
if device == 'mps':
    print('- Mac M2 建议使用较小的batch_size (1-2)')
    print('- 可以使用 load_in_4bit=False 以避免量化问题')
elif device == 'cpu':
    print('- CPU训练建议使用更小的模型或更少的训练数据')
"

echo
echo "=== 环境设置完成 ==="
echo
echo "⚠️  重要提醒: 请确保激活虚拟环境后再运行训练脚本!"
echo "如果您退出了终端，请重新激活虚拟环境:"
echo "  source venv/bin/activate"
echo
echo "现在您可以运行以下命令："
echo "  python run.py test               # 快速测试"
echo "  python examples/quick_rag_test.py # RAG功能测试"
echo "  python run.py train              # 开始训练"
echo "  python run.py interactive        # 交互式训练"
echo
echo "Mac M2 特别说明："
echo "✓ 自动创建虚拟环境以避免系统包冲突"
echo "✓ 支持 Apple Silicon MPS 加速"
echo "✓ 优化的依赖安装流程"
echo "✓ 如果Unsloth不可用，会自动降级到标准训练"
echo
echo "模型特性："
echo "✓ Qwen3-4B-Thinking-2507 思考模式"
echo "✓ 医学实体关系抽取优化"
echo "✓ 官方chat template支持"
echo "✓ RAG-Anything多模态文档处理"
echo "✓ LightRAG知识图谱检索"
echo "✓ HuggingFace模型上传支持"
echo
