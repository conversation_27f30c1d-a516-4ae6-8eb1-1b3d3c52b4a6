#!/bin/bash

# Medical Large Language Model Fine-tuning Quick Start Script
# Qwen3-4B-Thinking + RAG-Anything Integration

echo "=========================================="
echo "Medical LLM Fine-tuning"
echo "Qwen3-4B-Thinking + RAG-Anything"
echo "=========================================="

# Check Python environment
echo "Checking Python environment..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3 not installed"
    exit 1
fi

# Check if we're in a virtual environment or create one
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "Creating and activating virtual environment..."
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    source venv/bin/activate
    echo "✅ Virtual environment activated"
fi

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ Dependency installation failed"
    exit 1
fi

# Environment check
echo "Running environment check..."
python scripts/setup_environment.py
if [ $? -ne 0 ]; then
    echo "❌ Environment check failed"
    exit 1
fi

echo "✅ Environment setup complete!"
echo ""
echo "Choose execution mode:"
echo "1) Quick test (recommended first run)"
echo "2) RAG-Anything demo"
echo "3) Model training (Mac M2 optimized)"
echo "4) Model training (CUDA)"
echo "5) Model training (CPU)"
echo "6) Training with HuggingFace upload"
echo "7) Interactive mode"
echo "8) Full pipeline"
echo "9) Legacy pipeline (old system)"

read -p "Please select (1-9): " choice

case $choice in
    1)
        echo "Running quick test..."
        python run.py test
        ;;
    2)
        echo "Running RAG-Anything demo..."
        python examples/quick_rag_test.py
        echo ""
        echo "For comprehensive demo, run:"
        echo "python examples/medical_rag_demo.py"
        ;;
    3)
        echo "Training with Mac M2 optimization..."
        python run.py train
        ;;
    4)
        echo "Training with CUDA..."
        python run.py cuda
        ;;
    5)
        echo "Training with CPU..."
        python run.py cpu
        ;;
    6)
        echo "Training with HuggingFace upload..."
        python run.py train-upload
        ;;
    7)
        echo "Starting interactive mode..."
        python run.py interactive
        ;;
    8)
        echo "Running full pipeline..."
        python run.py full
        ;;
    9)
        echo "Running legacy pipeline..."
        python scripts/run_training_pipeline.py --stage all
        ;;
    *)
        echo "Invalid selection"
        exit 1
        ;;
esac

echo ""
echo "🎉 Execution completed!"
echo ""
echo "Generated files:"
echo "  - processed_data/: Processed training data"
echo "  - medical_llm_output/: Fine-tuned model"
echo "  - medical_rag_workspace/: RAG-Anything workspace"
echo "  - evaluation_results/: Evaluation results"
echo "  - logs/: Training and execution logs"
echo ""
echo "Next steps:"
echo "  - Test RAG functionality: python examples/quick_rag_test.py"
echo "  - Upload to HuggingFace: python run.py upload"
echo "  - Interactive training: python run.py interactive"

