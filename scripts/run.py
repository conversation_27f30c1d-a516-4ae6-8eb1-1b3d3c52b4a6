#!/usr/bin/env python3
"""
Simple wrapper script for Universal Medical LLM Trainer

This provides easy commands for different use cases:
- python run.py test          # Quick inference test
- python run.py train         # Full training pipeline
- python run.py interactive   # Interactive mode
- python run.py mac           # Mac M2 optimized
- python run.py cuda          # CUDA optimized
- python run.py cpu           # CPU fallback
"""

import sys
import subprocess
from pathlib import Path

def show_usage():
    """Show usage information"""
    print("🚀 Medical LLM Fine-tuning - Easy Runner")
    print("=" * 50)
    print()
    print("Usage: python run.py <command> [options]")
    print()
    print("Commands:")
    print("  test         Quick inference test (Mac M2 config)")
    print("  train        Full training pipeline")
    print("  train-upload Train and upload to HuggingFace")
    print("  upload       Upload existing model to HuggingFace")
    print("  interactive  Interactive inference mode")
    print("  mac          Mac M2/M3 optimized inference")
    print("  cuda         CUDA GPU optimized inference")
    print("  cpu          CPU fallback inference")
    print("  full         Complete pipeline (data + train + eval)")
    print("  full-upload  Complete pipeline + HuggingFace upload")
    print()
    print("Examples:")
    print("  python run.py test")
    print("  python run.py train")
    print("  python run.py train-upload")
    print("  python run.py upload")
    print("  python run.py interactive")
    print("  python run.py mac")
    print("  python run.py cuda")
    print("  python run.py cpu")
    print()
    print("Advanced:")
    print("  python run.py custom --config path/to/config.yaml --mode inference")
    print("  python run.py custom --config config/config_mac_m2.yaml --mode upload --model-path ./model")

def run_command(cmd_args):
    """Run universal trainer with given arguments"""
    base_cmd = [sys.executable, "universal_trainer.py"]
    full_cmd = base_cmd + cmd_args
    
    print(f"🔧 Running: {' '.join(full_cmd)}")
    print("=" * 50)
    
    try:
        result = subprocess.run(full_cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return False

def main():
    """Main function"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    if command in ['help', '-h', '--help']:
        show_usage()
        return
    
    # Define command mappings
    command_map = {
        'test': ['--config', 'config/config_mac_m2.yaml', '--mode', 'inference'],
        'train': ['--config', 'config/config_mac_m2.yaml', '--mode', 'train'],
        'train-upload': ['--config', 'config/config_mac_m2.yaml', '--mode', 'train', '--upload-to-hf'],
        'upload': ['--config', 'config/config_mac_m2.yaml', '--mode', 'upload'],
        'interactive': ['--config', 'config/config_mac_m2.yaml', '--mode', 'interactive'],
        'mac': ['--config', 'config/config_mac_m2.yaml', '--mode', 'inference'],
        'cuda': ['--config', 'config/config_cuda.yaml', '--mode', 'inference'],
        'cpu': ['--config', 'config/config_cpu.yaml', '--mode', 'inference'],
        'full': ['--config', 'config/config_mac_m2.yaml', '--mode', 'full'],
        'full-upload': ['--config', 'config/config_mac_m2.yaml', '--mode', 'full', '--upload-to-hf'],
        'custom': sys.argv[2:]  # Pass through remaining arguments
    }
    
    if command in command_map:
        args = command_map[command]
        success = run_command(args)
        
        if success:
            print("\n✅ Command completed successfully!")
        else:
            print("\n❌ Command failed!")
            sys.exit(1)
    else:
        print(f"❌ Unknown command: {command}")
        print()
        show_usage()
        sys.exit(1)

if __name__ == "__main__":
    main()
