#!/usr/bin/env python3
"""
Environment Setup and Dependency Check Script
Ensure medical LLM fine-tuning environment with RAG-Anything is properly configured
"""

import sys
import subprocess
import importlib
import torch
from pathlib import Path

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python version too low, requires Python 3.8+")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_gpu_availability():
    """Check GPU availability and determine optimal configuration"""
    import platform
    
    system = platform.system()
    arch = platform.machine()
    
    # Check for Apple Silicon MPS
    if system == "Darwin" and arch == "arm64":
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("✅ Apple Silicon MPS acceleration available")
            return "mps"
        else:
            print("⚠️  Apple Silicon detected but MPS not available")
            return "cpu"
    
    # Check for CUDA
    elif torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✅ Found {gpu_count} CUDA GPU(s):")
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        
        # Check if GPU memory is sufficient
        if gpu_memory < 12:
            print("⚠️  GPU memory might be insufficient, recommend 16GB+ for optimal performance")
        return "cuda"
    
    else:
        print("❌ No GPU acceleration available, will use CPU (very slow)")
        return "cpu"

def install_dependencies(device_type="auto"):
    """Install all required dependencies based on device type"""
    import os
    
    print("🔧 Installing dependencies...")
    
    # Determine device type if auto
    if device_type == "auto":
        device_type = check_gpu_availability()
    
    try:
        # Install core dependencies
        core_packages = [
            "torch", "torchvision", "torchaudio",
            "transformers>=4.35.0",
            "accelerate>=0.24.0",
            "datasets>=2.14.0",
            "tokenizers>=0.14.0",
            "peft>=0.6.0",
            "trl>=0.7.0"
        ]
        
        # Device-specific packages
        if device_type == "mps":
            # For Apple Silicon, avoid bitsandbytes issues
            print("   Installing Apple Silicon optimized packages...")
            device_packages = []
        elif device_type == "cuda":
            print("   Installing CUDA optimized packages...")
            device_packages = ["bitsandbytes>=0.41.0"]
        else:
            print("   Installing CPU-only packages...")
            device_packages = []
        
        # Additional ML packages
        ml_packages = [
            "scikit-learn>=1.3.0",
            "numpy>=1.24.0", 
            "pandas>=2.0.0",
            "sentence-transformers>=2.2.0",
            "tqdm>=4.65.0"
        ]
        
        # RAG and utility packages
        rag_packages = [
            "raganything[all]",
            "lightrag",
            "chromadb>=0.4.0",
            "python-dotenv>=1.0.0",
            "huggingface_hub>=0.20.0",
            "PyYAML>=6.0"
        ]
        
        # Optional packages
        optional_packages = [
            "wandb>=0.16.0"
        ]
        
        all_packages = core_packages + device_packages + ml_packages + rag_packages + optional_packages
        
        # Install packages in batches to handle potential conflicts
        for i, package in enumerate(all_packages):
            print(f"   Installing {package} ({i+1}/{len(all_packages)})...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"   ⚠️  Failed to install {package}: {result.stderr}")
                # Continue with other packages
            else:
                print(f"   ✅ {package} installed successfully")
        
        # Try to install Unsloth (may fail on some platforms)
        print("   Installing Unsloth (optional acceleration)...")
        unsloth_result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
        ], capture_output=True, text=True)
        
        if unsloth_result.returncode == 0:
            print("   ✅ Unsloth installed successfully")
        else:
            print("   ⚠️  Unsloth installation failed (will use standard training)")
        
        return True
        
    except Exception as e:
        print(f"❌ Dependency installation failed: {e}")
        return False

def check_installed_packages():
    """Check which packages are already installed"""
    required_packages = [
        "torch", "transformers", "peft", "accelerate", "datasets",
        "sentence_transformers", "numpy", "scikit_learn", "raganything"
    ]
    
    installed = []
    missing = []
    
    for package in required_packages:
        try:
            if package == "scikit_learn":
                # Special handling for sklearn
                import sklearn
                installed.append(f"scikit-learn: {sklearn.__version__}")
            else:
                module = importlib.import_module(package)
                if hasattr(module, '__version__'):
                    installed.append(f"{package}: {module.__version__}")
                else:
                    installed.append(f"{package}: installed")
        except ImportError:
            missing.append(package)
    
    if installed:
        print("✅ Installed packages:")
        for pkg in installed:
            print(f"   {pkg}")
    
    if missing:
        print("❌ Missing packages:")
        for pkg in missing:
            print(f"   {pkg}")
    
    return len(missing) == 0

def check_model_access():
    """Check Qwen model access"""
    try:
        from transformers import AutoTokenizer
        print("Testing Qwen3-4B-Thinking model access...")
        
        # Try to load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(
            "Qwen/Qwen3-4B-Thinking-2507",
            trust_remote_code=True,
            cache_dir="./model_cache"
        )
        print("✅ Qwen3-4B-Thinking model access successful")
        return True
        
    except Exception as e:
        print(f"❌ Qwen3-4B-Thinking model access failed: {str(e)}")
        print("Please check network connection or Hugging Face access permissions")
        return False

def check_rag_components():
    """Check RAG-Anything components"""
    try:
        from raganything import RAGAnything
        from lightrag import LightRAG
        print("✅ RAG-Anything components available")
        return True
    except ImportError as e:
        print(f"❌ RAG-Anything components not available: {e}")
        return False

def check_api_keys():
    """Check API key configuration"""
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    openai_key = os.getenv('OPENAI_API_KEY')
    hf_token = os.getenv('HF_TOKEN')
    
    if openai_key:
        print("✅ OpenAI API key found")
    else:
        print("⚠️  OpenAI API key not found (optional for RAG functionality)")
    
    if hf_token:
        print("✅ HuggingFace token found")
    else:
        print("⚠️  HuggingFace token not found (optional for model upload)")
    
    return True

def setup_directories():
    """Create necessary directories"""
    directories = [
        "processed_data",
        "medical_llm_output",
        "vector_db", 
        "evaluation_results",
        "logs",
        "model_cache",
        "medical_rag_workspace",
        "config"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ Created directory: {dir_name}")

def setup_configuration_files(device_type):
    """Create configuration files based on detected hardware"""
    import yaml
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # Base configuration template
    base_config = {
        'model': {
            'name': 'Qwen/Qwen3-4B-Thinking-2507',
            'max_seq_length': 2048 if device_type != "cpu" else 1024,
            'torch_dtype': 'float16' if device_type != "cpu" else 'float32',
            'device_map': None,
            'trust_remote_code': True,
            'use_unsloth': device_type in ["cuda", "mps"],
            'tags': ['medical', 'thinking', 'ner', 'qwen3'],
            'notes': 'Qwen3-4B-Thinking model for medical entity extraction'
        },
        'training': {
            'per_device_train_batch_size': 1 if device_type == "mps" else 2,
            'gradient_accumulation_steps': 8 if device_type == "mps" else 4,
            'warmup_steps': 10,
            'max_steps': 100,
            'learning_rate': 2.0e-4,
            'fp16': device_type != "cpu",
            'logging_steps': 1,
            'optim': 'adamw_8bit' if device_type == "cuda" else 'adamw_torch',
            'weight_decay': 0.01,
            'lr_scheduler_type': 'linear',
            'seed': 42,
            'dataloader_num_workers': 2 if device_type == "mps" else 4,
            'mixed_precision': 'fp16' if device_type != "cpu" else 'no'
        },
        'lora': {
            'r': 16,
            'lora_alpha': 32,
            'lora_dropout': 0.1,
            'bias': 'none',
            'task_type': 'CAUSAL_LM',
            'target_modules': ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
        },
        'quantization': {
            'load_in_4bit': device_type == "cuda",
            'bnb_4bit_use_double_quant': True,
            'bnb_4bit_quant_type': 'nf4',
            'bnb_4bit_compute_dtype': 'float16'
        },
        'data': {
            'train_file': 'output.json',
            'max_length': 2048 if device_type != "cpu" else 1024,
            'train_split': 0.9,
            'augmentation': False
        },
        'rag': {
            'working_dir': './medical_rag_workspace',
            'api_key_env': 'OPENAI_API_KEY',
            'base_url_env': 'OPENAI_BASE_URL',
            'model_name': 'gpt-4o-mini',
            'parser': 'mineru',
            'parse_method': 'auto',
            'chunk_size': 1200,
            'chunk_overlap': 100,
            'top_k': 5,
            'search_modes': {
                'default': 'hybrid',
                'quick': 'local',
                'comprehensive': 'global'
            },
            'multimodal': {
                'process_images': True,
                'process_tables': True,
                'process_equations': True,
                'image_description_detail': 'medium'
            }
        },
        'evaluation': {
            'eval_steps': 50,
            'eval_strategy': 'steps',
            'save_steps': 100,
            'save_strategy': 'steps',
            'save_total_limit': 3,
            'load_best_model_at_end': True,
            'metric_for_best_model': 'eval_loss'
        },
        'output': {
            'output_dir': './medical_llm_output',
            'logging_dir': './logs',
            'run_name': f'qwen3-medical-{device_type}',
            'report_to': ['tensorboard']
        },
        'hardware': {
            'device': device_type,
            'auto_find_batch_size': True
        }
    }
    
    # Device-specific adjustments
    if device_type == "mps":
        base_config['mac_m2'] = {
            'optimize_for_apple_silicon': True,
            'use_mps_fallback': True,
            'reduce_memory_usage': True
        }
    
    # Save configuration file
    config_file = config_dir / f"config_{device_type}.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(base_config, f, default_flow_style=False, indent=2)
    
    print(f"✅ Created configuration file: {config_file}")
    return str(config_file)

def setup_environment_file():
    """Create .env file template if it doesn't exist"""
    env_file = Path(".env")
    if not env_file.exists():
        env_template = """# Environment Variables for Medical LLM Training

# HuggingFace Token (for model upload)
HF_TOKEN=your_huggingface_token_here

# OpenAI API Key (for RAG-Anything functionality)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Custom OpenAI Base URL
# OPENAI_BASE_URL=https://api.openai.com/v1

# Optional: Weights & Biases API Key
# WANDB_API_KEY=your_wandb_api_key_here

# Optional: HuggingFace Hub Settings
# HF_HUB_ENABLE_HF_TRANSFER=1
# HF_HUB_DOWNLOAD_TIMEOUT=600
"""
        with open(env_file, 'w') as f:
            f.write(env_template)
        print("✅ Created .env template file")
        print("   Please edit .env file with your actual API keys")
    else:
        print("✅ .env file already exists")

def create_sample_data():
    """Create sample training data if output.json doesn't exist"""
    data_file = Path("output.json")
    if not data_file.exists():
        import json
        
        sample_data = [
            {
                "instruction": "Extract medical entities and relationships from the following text.",
                "input": "Hepatitis C virus (HCV) causes chronic liver infection and is associated with liver cirrhosis.",
                "output": "Entities:\n- Bacteria: Hepatitis C virus (HCV)\n- Disease: chronic liver infection, liver cirrhosis\n\nRelationships:\n- Hepatitis C virus (HCV) -> causes -> chronic liver infection\n- Hepatitis C virus (HCV) -> associated with -> liver cirrhosis"
            },
            {
                "instruction": "Analyze the medical text and extract entities with their relationships.",
                "input": "Streptococcus pneumoniae is a major bacterial pathogen causing pneumonia and meningitis.",
                "output": "Entities:\n- Bacteria: Streptococcus pneumoniae\n- Disease: pneumonia, meningitis\n\nRelationships:\n- Streptococcus pneumoniae -> causes -> pneumonia\n- Streptococcus pneumoniae -> causes -> meningitis"
            }
        ]
        
        with open(data_file, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print("✅ Created sample training data: output.json")
        print("   You can replace this with your own medical training data")
    else:
        print("✅ Training data file output.json already exists")

def main():
    """Main setup function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Medical LLM Environment Setup")
    parser.add_argument("--mode", choices=["check", "setup", "install"], default="setup",
                       help="Mode: check (verify only), setup (full setup), install (dependencies only)")
    parser.add_argument("--device", choices=["auto", "cuda", "mps", "cpu"], default="auto",
                       help="Target device type")
    parser.add_argument("--skip-deps", action="store_true",
                       help="Skip dependency installation")
    
    args = parser.parse_args()
    
    print("=" * 70)
    print("🏥 Medical LLM Fine-tuning Environment Setup")
    print("🤖 Qwen3-4B-Thinking + RAG-Anything Integration")
    print("=" * 70)
    
    # Step 1: Basic checks
    print("\n🔍 Step 1: System Analysis")
    print("-" * 30)
    
    if not check_python_version():
        print("❌ Python version check failed")
        sys.exit(1)
    
    device_type = check_gpu_availability() if args.device == "auto" else args.device
    print(f"🎯 Target device: {device_type}")
    
    # Step 2: Create directories
    print("\n📁 Step 2: Directory Setup")
    print("-" * 30)
    setup_directories()
    
    # Step 3: Install dependencies (unless checking only)
    if args.mode != "check" and not args.skip_deps:
        print("\n📦 Step 3: Dependency Installation")
        print("-" * 30)
        
        # Check current packages first
        print("Checking currently installed packages...")
        packages_installed = check_installed_packages()
        
        if not packages_installed:
            print("\nInstalling missing dependencies...")
            if not install_dependencies(device_type):
                print("❌ Dependency installation failed")
                sys.exit(1)
        else:
            print("✅ All required packages are already installed")
    
    # Step 4: Configuration setup (unless install-only)
    if args.mode != "install":
        print("\n⚙️  Step 4: Configuration Setup")
        print("-" * 30)
        
        config_file = setup_configuration_files(device_type)
        setup_environment_file()
        create_sample_data()
    
    # Step 5: Verification
    if args.mode != "install":
        print("\n🧪 Step 5: System Verification")
        print("-" * 30)
        
        # Check RAG components
        try:
            rag_ok = check_rag_components()
        except:
            rag_ok = False
            print("⚠️  RAG components check skipped (dependencies may still be installing)")
        
        # Check API keys
        check_api_keys()
        
        # Try to access model (optional)
        try:
            model_ok = check_model_access()
        except:
            model_ok = False
            print("⚠️  Model access check skipped (network or dependencies issue)")
    
    # Summary
    print("\n" + "=" * 70)
    print("🎉 Environment setup completed!")
    print("\n📋 What was set up:")
    print(f"   ✅ Target device: {device_type}")
    print(f"   ✅ Configuration: config/config_{device_type}.yaml")
    print("   ✅ Environment template: .env")
    print("   ✅ Sample data: output.json")
    print("   ✅ Required directories created")
    
    if args.mode != "check":
        print("\n🚀 Next Steps:")
        print("1. Edit .env file with your API keys:")
        print("   - HF_TOKEN for HuggingFace model upload")
        print("   - OPENAI_API_KEY for RAG functionality")
        print("\n2. Test the installation:")
        print("   python run.py test")
        print("\n3. Try RAG functionality:")
        print("   python examples/quick_rag_test.py")
        print("\n4. Start training:")
        print(f"   python universal_trainer.py --config config/config_{device_type}.yaml --mode train")
        print("\n5. Or use simplified commands:")
        print("   python run.py train    # Mac M2 optimized")
        print("   python run.py cuda     # CUDA GPU")
        print("   python run.py cpu      # CPU only")
    
    print("\n💡 Tips:")
    print("   - Run with --mode check to verify setup without changes")
    print("   - Run with --mode install to only install dependencies")
    print("   - Use --device to force a specific device type")
    print("   - Check logs/ directory for training logs")
    
    print("\n🔗 Documentation:")
    print("   - README.md: Complete project overview")
    print("   - docs/UNIVERSAL_TRAINER_GUIDE.md: Detailed training guide")
    print("   - examples/: Demo scripts and examples")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()

