#!/usr/bin/env python3
"""
Medical Large Language Model Fine-tuning Pipeline
Complete pipeline for medical entity relationship extraction using Qwen3-4B-Thinking + RAG-Anything
"""

import os
import sys
import json
import argparse
import asyncio
from pathlib import Path
import torch

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import dependencies with graceful fallback
MedicalDataProcessor = None
MedicalLLMTrainer = None
MedicalRAGSystem = None
MedicalNERREEvaluator = None

import_errors = []

try:
    from core.data_processing import MedicalDataProcessor
except ImportError as e:
    import_errors.append(f"MedicalDataProcessor: {e}")

try:
    from core.medical_llm_trainer import Medical<PERSON>MTrainer
except ImportError as e:
    import_errors.append(f"MedicalLLMTrainer: {e}")

try:
    from core.medical_rag_system import MedicalRAGSystem
except ImportError as e:
    import_errors.append(f"MedicalRAGSystem: {e}")

try:
    from core.evaluation_metrics import MedicalNERREEvaluator
except ImportError as e:
    import_errors.append(f"MedicalNERREEvaluator: {e}")

def check_dependencies():
    """Check if all required dependencies are available"""
    if import_errors:
        print("⚠️  Some dependencies are missing:")
        for error in import_errors:
            print(f"   - {error}")
        print("\n💡 To install missing dependencies:")
        print("   python scripts/setup_environment.py --mode install")
        print("   # or")
        print("   pip install -r requirements.txt")
        return False
    return True

def setup_environment():
    """Setup environment and dependencies"""
    import platform

    # Detect hardware and acceleration
    system = platform.system()
    arch = platform.machine()

    if system == "Darwin" and arch == "arm64":
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            print("✅ Apple Silicon MPS acceleration available")
            device = "mps"
        else:
            print("⚠️  Apple Silicon detected but MPS not available")
            device = "cpu"
    elif torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"✅ Found {gpu_count} CUDA GPU(s):")
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        device = "cuda"
    else:
        print("❌ No GPU acceleration available, using CPU")
        device = "cpu"

    # Create necessary directories
    directories = [
        "processed_data",
        "medical_llm_output",
        "medical_rag_workspace",
        "vector_db",
        "evaluation_results",
        "logs",
        "model_cache"
    ]

    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ Created directory: {dir_name}")

    return device

def stage1_data_preprocessing(data_path: str = "output.json"):
    """Stage 1: Data preprocessing and format conversion"""
    print("\n" + "="*60)
    print("Stage 1: Data Preprocessing & Format Conversion")
    print("="*60)
    
    if MedicalDataProcessor is None:
        print("❌ MedicalDataProcessor not available")
        print("💡 Install dependencies: python scripts/setup_environment.py --mode install")
        return False
    
    if not Path(data_path).exists():
        print(f"❌ Data file not found: {data_path}")
        print("Creating sample data...")
        create_sample_data(data_path)
    
    processor = MedicalDataProcessor(data_path)
    processor.load_data()

    print(f"📊 Loaded {len(processor.records)} records")

    # Data augmentation
    print("🔄 Performing data augmentation...")
    try:
        augmented_data = processor.augment_data(augment_ratio=0.2)
        processor.records.extend(augmented_data)
        print(f"✅ Augmented data count: {len(processor.records)}")
    except Exception as e:
        print(f"⚠️  Data augmentation failed: {e}")
        print("Continuing without augmentation...")

    # Save processed data
    print("💾 Saving processed data...")
    processor.save_processed_data("processed_data")

    print("✅ Stage 1 Complete: Data preprocessing")
    return True

def create_sample_data(data_path: str):
    """Create sample medical training data"""
    sample_data = [
        {
            "instruction": "Extract medical entities and relationships from the following text.",
            "input": "Hepatitis C virus (HCV) causes chronic liver infection and is associated with liver cirrhosis.",
            "output": "Entities:\n- Bacteria: Hepatitis C virus (HCV)\n- Disease: chronic liver infection, liver cirrhosis\n\nRelationships:\n- Hepatitis C virus (HCV) -> causes -> chronic liver infection\n- Hepatitis C virus (HCV) -> associated with -> liver cirrhosis"
        },
        {
            "instruction": "Analyze the medical text and extract entities with their relationships.",
            "input": "Streptococcus pneumoniae is a major bacterial pathogen causing pneumonia and meningitis.",
            "output": "Entities:\n- Bacteria: Streptococcus pneumoniae\n- Disease: pneumonia, meningitis\n\nRelationships:\n- Streptococcus pneumoniae -> causes -> pneumonia\n- Streptococcus pneumoniae -> causes -> meningitis"
        },
        {
            "instruction": "Extract medical entities and their relationships from the text.",
            "input": "Recent clinical studies demonstrate the efficacy of probiotics in reducing antibiotic-associated diarrhea.",
            "output": "Entities:\n- Evidence: Recent clinical studies\n- Disease: antibiotic-associated diarrhea\n\nRelationships:\n- Recent clinical studies -> demonstrate -> efficacy of probiotics\n- probiotics -> reduce -> antibiotic-associated diarrhea"
        }
    ]

    with open(data_path, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)

    print(f"✅ Created sample data: {data_path}")

def stage2_model_training(
    model_name: str = "Qwen/Qwen3-4B-Thinking-2507",
    device: str = "auto",
    epochs: int = 3,
    batch_size: int = 2,
    learning_rate: float = 2e-4
):
    """Stage 2: Model fine-tuning training"""
    print("\n" + "="*60)
    print("Stage 2: Qwen3-4B-Thinking Model Fine-tuning")
    print("="*60)
    
    if MedicalLLMTrainer is None:
        print("❌ MedicalLLMTrainer not available")
        print("💡 Install dependencies: python scripts/setup_environment.py --mode install")
        return False
    
    # Check for required data files
    required_files = [
        "processed_data/instruction_data.json"
    ]
    
    # Check if instruction data exists, if not use original data
    if not Path("processed_data/instruction_data.json").exists():
        if Path("processed_data/train_data.json").exists():
            required_files = ["processed_data/train_data.json"]
        else:
            print("⚠️  No processed training data found, using original data")
            required_files = ["output.json"]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            raise FileNotFoundError(f"❌ Data file not found: {file_path}")
    
    # Determine training parameters based on device
    use_qlora = device == "cuda"
    if device == "mps":
        print("🍎 Using Apple Silicon MPS optimization")
        batch_size = min(batch_size, 1)  # Reduce batch size for MPS
    elif device == "cpu":
        print("💻 Using CPU training (slow)")
        batch_size = min(batch_size, 1)
        use_qlora = False
    
    print(f"📊 Training parameters:")
    print(f"   Model: {model_name}")
    print(f"   Device: {device}")
    print(f"   Use QLoRA: {use_qlora}")
    print(f"   Batch size: {batch_size}")
    print(f"   Learning rate: {learning_rate}")
    print(f"   Epochs: {epochs}")
    
    # Create trainer
    try:
        trainer = MedicalLLMTrainer(
            model_name=model_name,
            output_dir="./medical_llm_output",
            use_qlora=use_qlora,
            max_seq_length=1024 if device == "cpu" else 2048
        )
        
        # Start training
        train_file = required_files[0]
        trainer.train(train_file)
        
        print("✅ Stage 2 Complete: Model fine-tuning")
        return True
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("💡 Try using the universal trainer instead:")
        print("   python universal_trainer.py --config config/config_mps.yaml --mode train")
        return False

async def stage3_build_rag_system():
    """Stage 3: Build RAG-Anything system"""
    print("\n" + "="*60)
    print("Stage 3: Build RAG-Anything Enhanced System")
    print("="*60)
    
    # Check for API keys
    import os
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  OPENAI_API_KEY not found in environment")
        print("RAG-Anything functionality will be limited")
        print("Please set your API key in .env file")
    
    try:
        # Initialize RAG-Anything system
        rag_system = MedicalRAGSystem(
            working_dir="./medical_rag_workspace",
            api_key=api_key,
            model_name="gpt-4o-mini",
            parser="mineru",
            parse_method="auto"
        )
        
        # Build knowledge base from training data
        print("📚 Building knowledge base from training data...")
        if Path("output.json").exists():
            await rag_system.build_knowledge_base_from_json("output.json")
            print("✅ Knowledge base built successfully")
        else:
            print("⚠️  No training data found for knowledge base")
        
        # Get system statistics
        stats = rag_system.get_stats()
        print("\n📊 RAG System Statistics:")
        print(f"   Working directory: {stats['working_dir']}")
        print(f"   Parser: {stats['parser']}")
        print(f"   Parse method: {stats['parse_method']}")
        print(f"   Components available: {list(k for k, v in stats['components'].items() if v)}")
        
        print("✅ Stage 3 Complete: RAG-Anything system built")
        return rag_system
        
    except Exception as e:
        print(f"❌ RAG system initialization failed: {e}")
        print("💡 Make sure to:")
        print("   1. Set OPENAI_API_KEY in .env file")
        print("   2. Install RAG-Anything: pip install 'raganything[all]'")
        print("   3. Check network connectivity")
        return None

async def stage4_evaluation(rag_system: MedicalRAGSystem = None):
    """Stage 4: Model performance evaluation"""
    print("\n" + "="*60)
    print("Stage 4: Model Performance Evaluation")
    print("="*60)
    
    # Load test data
    test_data_paths = [
        "processed_data/test_data.json",
        "processed_data/validation_data.json",
        "output.json"
    ]
    
    test_data_path = None
    for path in test_data_paths:
        if Path(path).exists():
            test_data_path = path
            break
    
    if not test_data_path:
        print("❌ No test data found")
        return False
    
    print(f"📊 Using test data: {test_data_path}")
    
    with open(test_data_path, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    print(f"📊 Evaluating {len(test_data)} samples...")
    
    # Initialize evaluator
    evaluator = MedicalNERREEvaluator()
    predictions = []
    ground_truths = []
    
    # If RAG system is available, use it for enhanced evaluation
    if rag_system:
        print("🔍 Using RAG-enhanced evaluation...")
        for i, item in enumerate(test_data):
            if i % 10 == 0:
                print(f"Progress: {i+1}/{len(test_data)}")
            
            try:
                # Use RAG system for inference
                if isinstance(item, dict) and "input" in item:
                    query = item["input"]
                    expected = item.get("output", "")
                elif isinstance(item, dict) and "text" in item:
                    query = item["text"]
                    expected = item.get("entities", "")
                else:
                    query = str(item)
                    expected = ""
                
                # Generate prediction with RAG
                result = await rag_system.generate_response(query, mode="hybrid")
                predictions.append(result)
                ground_truths.append(expected)
                
            except Exception as e:
                print(f"⚠️  Error processing item {i}: {e}")
                predictions.append("")
                ground_truths.append(expected if 'expected' in locals() else "")
    else:
        print("⚠️  No RAG system available, using basic evaluation...")
        # Basic evaluation without RAG
        for item in test_data:
            if isinstance(item, dict):
                predictions.append(item.get("output", ""))
                ground_truths.append(item.get("output", ""))
            else:
                predictions.append("")
                ground_truths.append("")
    
    # Save predictions
    Path("evaluation_results").mkdir(exist_ok=True)
    results = {
        "predictions": predictions,
        "ground_truths": ground_truths,
        "test_data_path": test_data_path,
        "num_samples": len(test_data)
    }
    
    with open("evaluation_results/predictions.json", 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # Perform evaluation
    try:
        print("📊 Computing evaluation metrics...")
        metrics = evaluator.evaluate_predictions(predictions, ground_truths)
        
        print("\n📈 Evaluation Results:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                print(f"   {metric}: {value:.4f}")
            else:
                print(f"   {metric}: {value}")
        
        # Save metrics
        with open("evaluation_results/metrics.json", 'w', encoding='utf-8') as f:
            json.dump(metrics, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        print(f"⚠️  Evaluation metrics computation failed: {e}")
    
    print("✅ Stage 4 Complete: Model evaluation")
    return True

async def stage5_demo_inference(rag_system: MedicalRAGSystem = None):
    """Stage 5: Demonstration inference"""
    print("\n" + "="*60)
    print("Stage 5: Medical RAG-Anything Inference Demo")
    print("="*60)
    
    if not rag_system:
        print("❌ No RAG system available for inference demo")
        print("💡 Run stage 3 first to build the RAG system")
        return False
    
    # Test cases for medical entity extraction
    test_cases = [
        {
            "title": "Hepatitis C and Liver Disease",
            "text": """Hepatitis C virus (HCV) is a major cause of chronic liver disease worldwide. 
            The virus can establish persistent infection in hepatocytes, leading to 
            progressive liver fibrosis and eventually cirrhosis. Recent studies have 
            shown that HCV genotype 1 is the most prevalent strain globally."""
        },
        {
            "title": "Multiple Sclerosis and EBV",
            "text": """Multiple sclerosis (MS) is an autoimmune disease affecting the central nervous system.
            Epstein-Barr virus (EBV) infection has been proposed as a potential trigger for MS development.
            High antibody titers against EBV were observed in MS patients compared to healthy controls."""
        },
        {
            "title": "COVID-19 and Diabetes",
            "text": """COVID-19 patients with diabetes mellitus show increased severity and mortality rates.
            SARS-CoV-2 infection appears to exacerbate pre-existing diabetic complications.
            Inflammatory markers such as IL-6 and TNF-alpha are elevated in these patients."""
        }
    ]
    
    demo_results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['title']} ---")
        print(f"Input text: {test_case['text'][:100]}...")
        
        try:
            # RAG-enhanced inference with different search modes
            search_modes = ["local", "global", "hybrid"]
            
            for mode in search_modes:
                print(f"\n🔍 Using {mode} search mode:")
                result = await rag_system.generate_response(
                    f"Extract medical entities and relationships from: {test_case['text']}",
                    mode=mode
                )
                
                print(f"Response: {result[:200]}..." if len(result) > 200 else result)
                
                demo_results.append({
                    "test_case": i,
                    "title": test_case['title'],
                    "search_mode": mode,
                    "input": test_case['text'],
                    "output": result
                })
            
        except Exception as e:
            print(f"⚠️  Error in test case {i}: {e}")
            demo_results.append({
                "test_case": i,
                "title": test_case['title'],
                "error": str(e)
            })
        
        print("-" * 60)
    
    # Save demo results
    Path("evaluation_results").mkdir(exist_ok=True)
    with open("evaluation_results/demo_results.json", 'w', encoding='utf-8') as f:
        json.dump(demo_results, f, ensure_ascii=False, indent=2)
    
    print("✅ Stage 5 Complete: Inference demonstration")
    print(f"📊 Demo results saved to: evaluation_results/demo_results.json")
    
    # Show RAG system statistics
    stats = rag_system.get_stats()
    print("\n📊 Final RAG System Statistics:")
    print(f"   Components active: {sum(1 for v in stats['components'].values() if v)}")
    print(f"   Working directory: {stats['working_dir']}")
    
    return True

async def main():
    """Main function for medical LLM training pipeline"""
    parser = argparse.ArgumentParser(
        description="Medical Large Language Model Fine-tuning Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_training_pipeline.py --stage all
  python scripts/run_training_pipeline.py --stage 1 --data custom_data.json
  python scripts/run_training_pipeline.py --stage 2 --model Qwen/Qwen3-4B-Thinking-2507
  python scripts/run_training_pipeline.py --stage 3,4,5
        """
    )
    
    parser.add_argument("--stage", type=str, choices=["all", "1", "2", "3", "4", "5"], 
                       default="all", help="Pipeline stage to execute")
    parser.add_argument("--model", type=str, default="Qwen/Qwen3-4B-Thinking-2507",
                       help="Base model name")
    parser.add_argument("--epochs", type=int, default=3, help="Training epochs")
    parser.add_argument("--batch_size", type=int, default=2, help="Batch size")
    parser.add_argument("--lr", type=float, default=2e-4, help="Learning rate")
    parser.add_argument("--data", type=str, default="output.json", help="Input data file")
    parser.add_argument("--device", type=str, choices=["auto", "cuda", "mps", "cpu"], 
                       default="auto", help="Training device")
    
    args = parser.parse_args()
    
    print("=" * 70)
    print("🏥 Medical Large Language Model Fine-tuning Pipeline")
    print("🤖 Qwen3-4B-Thinking + RAG-Anything Integration")
    print("=" * 70)
    print(f"📊 Base model: {args.model}")
    print(f"🎯 Execution stage: {args.stage}")
    print(f"💾 Data file: {args.data}")
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Cannot proceed without required dependencies")
        print("Please install dependencies first and try again.")
        return
    
    # Setup environment and detect device
    detected_device = setup_environment()
    device = detected_device if args.device == "auto" else args.device
    print(f"🖥️  Target device: {device}")
    
    rag_system = None
    results = {"stages_completed": [], "errors": []}
    
    try:
        # Stage 1: Data preprocessing
        if args.stage == "all" or args.stage == "1":
            print(f"\n🚀 Starting Stage 1...")
            success = stage1_data_preprocessing(args.data)
            if success:
                results["stages_completed"].append("Stage 1: Data Preprocessing")
            else:
                results["errors"].append("Stage 1 failed")
        
        # Stage 2: Model training
        if args.stage == "all" or args.stage == "2":
            print(f"\n🚀 Starting Stage 2...")
            success = stage2_model_training(
                model_name=args.model,
                device=device,
                epochs=args.epochs,
                batch_size=args.batch_size,
                learning_rate=args.lr
            )
            if success:
                results["stages_completed"].append("Stage 2: Model Training")
            else:
                results["errors"].append("Stage 2 failed")
        
        # Stage 3: RAG system building
        if args.stage == "all" or args.stage == "3":
            print(f"\n🚀 Starting Stage 3...")
            rag_system = await stage3_build_rag_system()
            if rag_system:
                results["stages_completed"].append("Stage 3: RAG System Building")
            else:
                results["errors"].append("Stage 3 failed")
        
        # Stage 4: Evaluation
        if args.stage == "all" or args.stage == "4":
            print(f"\n🚀 Starting Stage 4...")
            # Initialize RAG system if not available
            if rag_system is None:
                try:
                    import os
                    from dotenv import load_dotenv
                    load_dotenv()
                    api_key = os.getenv('OPENAI_API_KEY')
                    
                    rag_system = MedicalRAGSystem(
                        working_dir="./medical_rag_workspace",
                        api_key=api_key
                    )
                except Exception as e:
                    print(f"⚠️  Could not initialize RAG system for evaluation: {e}")
            
            success = await stage4_evaluation(rag_system)
            if success:
                results["stages_completed"].append("Stage 4: Model Evaluation")
            else:
                results["errors"].append("Stage 4 failed")
        
        # Stage 5: Demo inference
        if args.stage == "all" or args.stage == "5":
            print(f"\n🚀 Starting Stage 5...")
            # Initialize RAG system if not available
            if rag_system is None:
                try:
                    import os
                    from dotenv import load_dotenv
                    load_dotenv()
                    api_key = os.getenv('OPENAI_API_KEY')
                    
                    rag_system = MedicalRAGSystem(
                        working_dir="./medical_rag_workspace",
                        api_key=api_key
                    )
                except Exception as e:
                    print(f"⚠️  Could not initialize RAG system for demo: {e}")
            
            success = await stage5_demo_inference(rag_system)
            if success:
                results["stages_completed"].append("Stage 5: Demo Inference")
            else:
                results["errors"].append("Stage 5 failed")
        
        # Final summary
        print("\n" + "="*70)
        print("🎉 Medical LLM Fine-tuning Pipeline Execution Complete!")
        print("="*70)
        
        print(f"\n✅ Completed Stages ({len(results['stages_completed'])}):")
        for stage in results["stages_completed"]:
            print(f"   ✓ {stage}")
        
        if results["errors"]:
            print(f"\n⚠️  Errors ({len(results['errors'])}):")
            for error in results["errors"]:
                print(f"   ✗ {error}")
        
        print("\n📁 Generated Files and Directories:")
        print("   - processed_data/: Processed training data")
        print("   - medical_llm_output/: Fine-tuned model weights")
        print("   - medical_rag_workspace/: RAG-Anything workspace")
        print("   - evaluation_results/: Evaluation metrics and results")
        print("   - logs/: Training and execution logs")
        
        print("\n🚀 Quick Commands:")
        print("   python scripts/run_training_pipeline.py --stage 2  # Training only")
        print("   python scripts/run_training_pipeline.py --stage 4  # Evaluation only")
        print("   python scripts/run_training_pipeline.py --stage 5  # Demo inference")
        
        print("\n💡 Alternative Tools:")
        print("   python universal_trainer.py --config config/config_mps.yaml --mode train")
        print("   python examples/quick_rag_test.py  # Quick RAG test")
        print("   python examples/medical_rag_demo.py  # Comprehensive RAG demo")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Pipeline interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Pipeline execution error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def run_pipeline():
    """Entry point that handles async execution"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Pipeline interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_pipeline()