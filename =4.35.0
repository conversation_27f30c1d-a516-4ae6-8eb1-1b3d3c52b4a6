Requirement already satisfied: transformers in ./venv/lib/python3.10/site-packages (4.55.4)
Requirement already satisfied: packaging>=20.0 in ./venv/lib/python3.10/site-packages (from transformers) (25.0)
Requirement already satisfied: tokenizers<0.22,>=0.21 in ./venv/lib/python3.10/site-packages (from transformers) (0.21.4)
Requirement already satisfied: safetensors>=0.4.3 in ./venv/lib/python3.10/site-packages (from transformers) (0.6.2)
Requirement already satisfied: huggingface-hub<1.0,>=0.34.0 in ./venv/lib/python3.10/site-packages (from transformers) (0.34.4)
Requirement already satisfied: tqdm>=4.27 in ./venv/lib/python3.10/site-packages (from transformers) (4.67.1)
Requirement already satisfied: requests in ./venv/lib/python3.10/site-packages (from transformers) (2.32.5)
Requirement already satisfied: regex!=2019.12.17 in ./venv/lib/python3.10/site-packages (from transformers) (2025.7.34)
Requirement already satisfied: filelock in ./venv/lib/python3.10/site-packages (from transformers) (3.19.1)
Requirement already satisfied: numpy>=1.17 in ./venv/lib/python3.10/site-packages (from transformers) (2.2.6)
Requirement already satisfied: pyyaml>=5.1 in ./venv/lib/python3.10/site-packages (from transformers) (6.0.2)
Requirement already satisfied: fsspec>=2023.5.0 in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.34.0->transformers) (2025.3.0)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.34.0->transformers) (1.1.8)
Requirement already satisfied: typing-extensions>=3.7.4.3 in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.34.0->transformers) (4.15.0)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.10/site-packages (from requests->transformers) (3.4.3)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.10/site-packages (from requests->transformers) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.10/site-packages (from requests->transformers) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.10/site-packages (from requests->transformers) (2025.8.3)
