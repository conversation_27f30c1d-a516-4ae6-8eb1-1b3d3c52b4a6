data:
  augmentation: false
  max_length: 2048
  train_file: output.json
  train_split: 0.9
evaluation:
  eval_steps: 50
  eval_strategy: steps
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  save_steps: 100
  save_strategy: steps
  save_total_limit: 3
hardware:
  auto_find_batch_size: true
  device: mps
lora:
  bias: none
  lora_alpha: 32
  lora_dropout: 0.1
  r: 16
  target_modules:
  - q_proj
  - k_proj
  - v_proj
  - o_proj
  - gate_proj
  - up_proj
  - down_proj
  task_type: CAUSAL_LM
mac_m2:
  optimize_for_apple_silicon: true
  reduce_memory_usage: true
  use_mps_fallback: true
model:
  device_map: null
  max_seq_length: 2048
  name: Qwen/Qwen3-4B-Thinking-2507
  notes: Qwen3-4B-Thinking model for medical entity extraction
  tags:
  - medical
  - thinking
  - ner
  - qwen3
  torch_dtype: float16
  trust_remote_code: true
  use_unsloth: true
output:
  logging_dir: ./logs
  output_dir: ./medical_llm_output
  report_to:
  - tensorboard
  run_name: qwen3-medical-mps
quantization:
  bnb_4bit_compute_dtype: float16
  bnb_4bit_quant_type: nf4
  bnb_4bit_use_double_quant: true
  load_in_4bit: false
rag:
  api_key_env: OPENAI_API_KEY
  base_url_env: OPENAI_BASE_URL
  chunk_overlap: 100
  chunk_size: 1200
  model_name: gpt-4o-mini
  multimodal:
    image_description_detail: medium
    process_equations: true
    process_images: true
    process_tables: true
  parse_method: auto
  parser: mineru
  search_modes:
    comprehensive: global
    default: hybrid
    quick: local
  top_k: 5
  working_dir: ./medical_rag_workspace
training:
  dataloader_num_workers: 2
  fp16: true
  gradient_accumulation_steps: 8
  learning_rate: 0.0002
  logging_steps: 1
  lr_scheduler_type: linear
  max_steps: 100
  mixed_precision: fp16
  optim: adamw_torch
  per_device_train_batch_size: 1
  seed: 42
  warmup_steps: 10
  weight_decay: 0.01
