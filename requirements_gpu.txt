# 医学大语言模型微调依赖包 - GPU优化版本 (CUDA 12.2)
# 核心框架 - GPU版本
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
transformers>=4.35.0
tokenizers>=0.14.0
datasets>=2.14.0
accelerate>=0.24.0

# 高效微调 - GPU优化
peft>=0.6.0
bitsandbytes>=0.41.0
scipy>=1.10.0
trl>=0.7.0

# 训练和评估
wandb>=0.16.0
scikit-learn>=1.3.0
numpy>=1.24.0
pandas>=2.0.0

# RAG系统 - 修复版本
sentence-transformers>=2.2.0
raganything[all]
lightrag-hku
chromadb>=0.4.0

# 工具库
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0
jupyter>=1.0.0
python-dotenv>=1.0.0
huggingface_hub>=0.20.0
PyYAML>=6.0

# Qwen模型特殊功能
modelscope>=1.9.0
transformers_stream_generator>=0.0.4

# 开发工具
black>=23.0.0
flake8>=6.0.0
pytest>=7.0.0

# GPU特定依赖
nvidia-ml-py3>=7.352.0
