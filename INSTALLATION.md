# Installation Guide

## Quick Installation

### Basic Installation
```bash
pip install medllm-finetune-rag
```

### With Optional Features
```bash
# RAG functionality
pip install medllm-finetune-rag[rag]

# GPU support
pip install medllm-finetune-rag[gpu]

# Development tools
pip install medllm-finetune-rag[dev]

# Visualization tools
pip install medllm-finetune-rag[viz]

# All features (except unsloth)
pip install medllm-finetune-rag[all]
```

## Manual Installation of Unsloth

⚠️ **Important**: Due to PyPI restrictions on git dependencies, Unsloth must be installed manually.

### For CUDA GPUs
```bash
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

### For Mac M1/M2/M3
```bash
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

### For CPU-only
```bash
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

## Complete Installation with Unsloth

```bash
# 1. Install the main package
pip install medllm-finetune-rag[all]

# 2. Install Unsloth manually
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
```

## Verification

After installation, verify everything works:

```bash
# Check system info
medllm info

# Test import
python -c "import medllm; print('✅ Installation successful!')"
```

## Troubleshooting

### Common Issues

1. **Unsloth not found**
   ```
   Warning: Unsloth not available. Falling back to standard training.
   ```
   **Solution**: Install Unsloth manually as shown above.

2. **bitsandbytes GPU warning**
   ```
   The installed version of bitsandbytes was compiled without GPU support.
   ```
   **Solution**: This is normal on Mac M1/M2. GPU quantization will use MPS instead.

3. **RAG dependencies missing**
   ```
   ModuleNotFoundError: No module named 'raganything'
   ```
   **Solution**: Install with RAG support: `pip install medllm-finetune-rag[rag]`

### Platform-Specific Notes

#### Mac M1/M2/M3
- Use MPS acceleration automatically
- bitsandbytes GPU warning is expected and can be ignored
- Unsloth installation may take longer due to compilation

#### NVIDIA GPUs
- Ensure CUDA toolkit is installed
- Use `pip install medllm-finetune-rag[gpu]` for GPU-optimized packages

#### CPU-only
- All features work but training will be slower
- Consider using smaller models and batch sizes

## Development Installation

For development or contributing:

```bash
# Clone the repository
git clone https://github.com/chenxingqiang/medllm-finetune-rag.git
cd medllm-finetune-rag

# Install in development mode
pip install -e .[dev,all]

# Install Unsloth manually
pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"

# Run tests
pytest tests/
```
