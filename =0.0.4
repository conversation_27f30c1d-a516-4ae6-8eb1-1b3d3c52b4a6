Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Collecting bitsandbytes
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/9c/40/91f1a5a694f434bc13cba160045fdc4e867032e627b001bf411048fefd9c/bitsandbytes-0.47.0-py3-none-manylinux_2_24_x86_64.whl (61.3 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 61.3/61.3 MB 11.4 MB/s eta 0:00:00
Collecting scipy
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/8e/6d/41991e503e51fc1134502694c5fa7a1671501a17ffa12716a4a9151af3df/scipy-1.15.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (37.7 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 37.7/37.7 MB 15.6 MB/s eta 0:00:00
Collecting scikit-learn
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/fb/a4/e488acdece6d413f370a9589a7193dac79cd486b2e418d3276d6ea0b9305/scikit_learn-1.7.1-cp310-cp310-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (9.7 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 9.7/9.7 MB 6.7 MB/s eta 0:00:00
Collecting wandb
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a9/54/c0a087114ff1bb6c32e64aaa58aea4342cebc0ad58b1378c0a5a831d2508/wandb-0.21.1-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.4 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 22.4/22.4 MB 11.2 MB/s eta 0:00:00
Collecting modelscope
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/94/b9/29190a9ad8ead9e0489e39aa3ebc36d528ed29c80ed4827d3adeb5a6130e/modelscope-1.29.1-py3-none-any.whl (5.9 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 5.9/5.9 MB 23.9 MB/s eta 0:00:00
Collecting transformers_stream_generator
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/42/c2/65f13aec253100e1916e9bd7965fe17bde796ebabeb1265f45191ab4ddc0/transformers-stream-generator-0.0.5.tar.gz (13 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: torch<3,>=2.2 in ./venv/lib/python3.10/site-packages (from bitsandbytes) (2.8.0)
Requirement already satisfied: numpy>=1.17 in ./venv/lib/python3.10/site-packages (from bitsandbytes) (2.2.6)
Collecting threadpoolctl>=3.1.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/32/d5/f9a850d79b0851d1d4ef6456097579a9005b31fea68726a4ae5f2d82ddd9/threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Collecting joblib>=1.2.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/7d/4f/1195bbac8e0c2acc5f740661631d8d750dc38d4a32b23ee5df3cde6f4e0d/joblib-1.5.1-py3-none-any.whl (307 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 307.7/307.7 kB 19.2 MB/s eta 0:00:00
Collecting platformdirs
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/40/4b/2028861e724d3bd36227adfa20d3fd24c3fc6d52032f4a93c133be5d17ce/platformdirs-4.4.0-py3-none-any.whl (18 kB)
Requirement already satisfied: packaging in ./venv/lib/python3.10/site-packages (from wandb) (25.0)
Requirement already satisfied: pydantic<3 in ./venv/lib/python3.10/site-packages (from wandb) (2.11.7)
Requirement already satisfied: pyyaml in ./venv/lib/python3.10/site-packages (from wandb) (6.0.2)
Requirement already satisfied: typing-extensions<5,>=4.8 in ./venv/lib/python3.10/site-packages (from wandb) (4.15.0)
Collecting click!=8.0.0,>=7.1
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl (102 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 102.2/102.2 kB 5.3 MB/s eta 0:00:00
Collecting protobuf!=4.21.0,!=5.28.0,<7,>=3.19.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/40/01/2e730bd1c25392fc32e3268e02446f0d77cb51a2c3a8486b1798e34d5805/protobuf-6.32.0-cp39-abi3-manylinux2014_x86_64.whl (322 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 322.0/322.0 kB 34.3 MB/s eta 0:00:00
Requirement already satisfied: requests<3,>=2.0.0 in ./venv/lib/python3.10/site-packages (from wandb) (2.32.5)
Collecting gitpython!=3.1.29,>=1.0.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/01/61/d4b89fec821f72385526e1b9d9a3a0385dda4a72b206d28049e2c7cd39b8/gitpython-3.1.45-py3-none-any.whl (208 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 208.2/208.2 kB 22.1 MB/s eta 0:00:00
Collecting sentry-sdk>=2.0.0
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/62/1f/5feb6c42cc30126e9574eabc28139f8c626b483a47c537f648d133628df0/sentry_sdk-2.35.1-py2.py3-none-any.whl (363 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 363.9/363.9 kB 27.1 MB/s eta 0:00:00
Requirement already satisfied: urllib3>=1.26 in ./venv/lib/python3.10/site-packages (from modelscope) (2.5.0)
Requirement already satisfied: tqdm>=4.64.0 in ./venv/lib/python3.10/site-packages (from modelscope) (4.67.1)
Requirement already satisfied: setuptools in ./venv/lib/python3.10/site-packages (from modelscope) (65.5.0)
Requirement already satisfied: filelock in ./venv/lib/python3.10/site-packages (from modelscope) (3.19.1)
Requirement already satisfied: transformers>=4.26.1 in ./venv/lib/python3.10/site-packages (from transformers_stream_generator) (4.55.4)
Collecting gitdb<5,>=4.0.1
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a0/61/5c78b91c3143ed5c14207f463aecfc8f9dbb5092fb2869baf37c273b2705/gitdb-4.0.12-py3-none-any.whl (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 8.9 MB/s eta 0:00:00
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.10/site-packages (from pydantic<3->wandb) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.10/site-packages (from pydantic<3->wandb) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.10/site-packages (from pydantic<3->wandb) (0.4.1)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.10/site-packages (from requests<3,>=2.0.0->wandb) (2025.8.3)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.10/site-packages (from requests<3,>=2.0.0->wandb) (3.10)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.10/site-packages (from requests<3,>=2.0.0->wandb) (3.4.3)
Requirement already satisfied: nvidia-cuda-runtime-cu12==12.8.90 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.90)
Requirement already satisfied: nvidia-nvtx-cu12==12.8.90 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.90)
Requirement already satisfied: nvidia-nvjitlink-cu12==12.8.93 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.93)
Requirement already satisfied: nvidia-cusparse-cu12==12.5.8.93 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.5.8.93)
Requirement already satisfied: nvidia-cudnn-cu12==9.10.2.21 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (9.10.2.21)
Requirement already satisfied: networkx in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.4.2)
Requirement already satisfied: fsspec in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (2025.3.0)
Requirement already satisfied: nvidia-cufft-cu12==11.3.3.83 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (11.3.3.83)
Requirement already satisfied: nvidia-nccl-cu12==2.27.3 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (2.27.3)
Requirement already satisfied: nvidia-cusolver-cu12==11.7.3.90 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (11.7.3.90)
Requirement already satisfied: triton==3.4.0 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.4.0)
Requirement already satisfied: nvidia-cublas-cu12==12.8.4.1 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.4.1)
Requirement already satisfied: nvidia-curand-cu12==10.3.9.90 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (10.3.9.90)
Requirement already satisfied: jinja2 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (3.1.6)
Requirement already satisfied: nvidia-cufile-cu12==1.13.1.3 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (1.13.1.3)
Requirement already satisfied: sympy>=1.13.3 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (1.14.0)
Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.8.93 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.93)
Requirement already satisfied: nvidia-cusparselt-cu12==0.7.1 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (0.7.1)
Requirement already satisfied: nvidia-cuda-cupti-cu12==12.8.90 in ./venv/lib/python3.10/site-packages (from torch<3,>=2.2->bitsandbytes) (12.8.90)
Requirement already satisfied: tokenizers<0.22,>=0.21 in ./venv/lib/python3.10/site-packages (from transformers>=4.26.1->transformers_stream_generator) (0.21.4)
Requirement already satisfied: regex!=2019.12.17 in ./venv/lib/python3.10/site-packages (from transformers>=4.26.1->transformers_stream_generator) (2025.7.34)
Requirement already satisfied: huggingface-hub<1.0,>=0.34.0 in ./venv/lib/python3.10/site-packages (from transformers>=4.26.1->transformers_stream_generator) (0.34.4)
Requirement already satisfied: safetensors>=0.4.3 in ./venv/lib/python3.10/site-packages (from transformers>=4.26.1->transformers_stream_generator) (0.6.2)
Collecting smmap<6,>=3.0.1
  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/04/be/d09147ad1ec7934636ad912901c5fd7667e1c858e19d355237db0d0cd5e4/smmap-5.0.2-py3-none-any.whl (24 kB)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in ./venv/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.34.0->transformers>=4.26.1->transformers_stream_generator) (1.1.8)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./venv/lib/python3.10/site-packages (from sympy>=1.13.3->torch<3,>=2.2->bitsandbytes) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in ./venv/lib/python3.10/site-packages (from jinja2->torch<3,>=2.2->bitsandbytes) (3.0.2)
Installing collected packages: threadpoolctl, smmap, sentry-sdk, scipy, protobuf, platformdirs, joblib, click, scikit-learn, modelscope, gitdb, gitpython, wandb, bitsandbytes, transformers_stream_generator
  Running setup.py install for transformers_stream_generator: started
  Running setup.py install for transformers_stream_generator: finished with status 'done'
Successfully installed bitsandbytes-0.47.0 click-8.2.1 gitdb-4.0.12 gitpython-3.1.45 joblib-1.5.1 modelscope-1.29.1 platformdirs-4.4.0 protobuf-6.32.0 scikit-learn-1.7.1 scipy-1.15.3 sentry-sdk-2.35.1 smmap-5.0.2 threadpoolctl-3.6.0 transformers_stream_generator-0.0.5 wandb-0.21.1
