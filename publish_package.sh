#!/bin/bash

# MedLLM PyPI Package Publishing Script
# This script builds and publishes the package to PyPI

set -e  # Exit on any error

echo "🏥 MedLLM Package Publishing Script"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "setup.py" ] || [ ! -f "pyproject.toml" ]; then
    echo "❌ Error: setup.py or pyproject.toml not found. Are you in the project root?"
    exit 1
fi

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    echo "⚠️  Virtual environment not detected. Activating venv..."
    source venv/bin/activate || {
        echo "❌ Error: Could not activate virtual environment"
        exit 1
    }
fi

# Install/upgrade build tools
echo "📦 Installing build tools..."
pip install --upgrade build twine

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist/ build/ *.egg-info/

# Build the package
echo "🔨 Building package..."
python -m build

# Check the built packages
echo "🔍 Checking built packages..."
python -m twine check dist/*

# List built files
echo "📋 Built files:"
ls -la dist/

echo ""
echo "✅ Package built successfully!"
echo ""
echo "📤 To publish to PyPI:"
echo "   Test PyPI: python -m twine upload --repository testpypi dist/*"
echo "   Real PyPI: python -m twine upload dist/*"
echo ""
echo "🔐 Make sure you have configured your PyPI credentials:"
echo "   pip install keyring"
echo "   python -m twine configure"
echo ""
echo "🎯 Installation after publishing:"
echo "   pip install medllm-finetune-rag"
echo "   pip install medllm-finetune-rag[all]  # with all features"
