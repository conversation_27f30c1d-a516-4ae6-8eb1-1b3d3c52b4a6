[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "medllm-finetune-rag"
version = "0.1.2"
description = "A comprehensive toolkit for fine-tuning medical large language models with RAG capabilities"
readme = "README.md"
license = "MIT"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Healthcare Industry", 
    "Intended Audience :: Science/Research",

    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
keywords = ["medical", "llm", "fine-tuning", "rag", "qwen", "healthcare", "nlp", "ai"]
requires-python = ">=3.8"
dependencies = [
    "torch>=2.0.0",
    "transformers>=4.35.0", 
    "tokenizers>=0.14.0",
    "datasets>=2.14.0",
    "accelerate>=0.24.0",
    "peft>=0.6.0",
    "trl>=0.7.0",
    "scikit-learn>=1.3.0",
    "numpy>=1.24.0",
    "pandas>=2.0.0",
    "sentence-transformers>=2.2.0",
    "raganything[all]",
    "lightrag",
    "chromadb>=0.4.0",
    "tqdm>=4.65.0",
    "python-dotenv>=1.0.0",
    "huggingface-hub>=0.20.0",
    "PyYAML>=6.0",
]

[project.optional-dependencies]
unsloth = [
    # Note: unsloth requires manual installation from GitHub
    # pip install "unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git"
    "unsloth_zoo",
]
rag = [
    # Core RAG dependencies are now included in base installation
]
gpu = [
    "bitsandbytes>=0.41.0",
]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0", 
    "pytest>=7.0.0",
    "jupyter>=1.0.0",
]
viz = [
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "wandb>=0.16.0",
]
all = [
    # Note: unsloth requires manual installation from GitHub
    "unsloth_zoo",
    # Core RAG dependencies (raganything, lightrag, chromadb) are now included in base installation
    "bitsandbytes>=0.41.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "wandb>=0.16.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "pytest>=7.0.0",
    "jupyter>=1.0.0",
]

[project.scripts]
medllm = "medllm.cli:main"
medllm-train = "medllm.cli:train_command"
medllm-eval = "medllm.cli:eval_command"
medllm-rag = "medllm.cli:rag_command"

[project.urls]
Homepage = "https://github.com/chenxingqiang/medllm-finetune-rag"
Repository = "https://github.com/chenxingqiang/medllm-finetune-rag"
Documentation = "https://github.com/chenxingqiang/medllm-finetune-rag/blob/main/README.md"
"Bug Reports" = "https://github.com/chenxingqiang/medllm-finetune-rag/issues"

[tool.setuptools.packages.find]
exclude = ["tests*", "scripts*", "examples*", "docs*"]

[tool.setuptools.package-data]
medllm = ["configs/*.yaml", "templates/*.txt", "data/*.json"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [".git", "__pycache__", "build", "dist", ".eggs"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

