"""
Tests for utility functions
"""

import pytest
import json
import tempfile
from pathlib import Path

from medllm.utils import (
    detect_device, validate_data_format, create_sample_data,
    format_memory_size, get_model_size
)


class TestDeviceDetection:
    def test_detect_device_returns_string(self):
        device = detect_device()
        assert isinstance(device, str)
        assert device in ["cuda", "mps", "cpu"]


class TestDataValidation:
    def test_validate_valid_data(self):
        # Create valid test data
        valid_data = [
            {
                "instruction": "Test instruction",
                "input": "Test input", 
                "output": "Test output"
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(valid_data, f)
            temp_path = f.name
        
        is_valid, errors = validate_data_format(temp_path)
        
        assert is_valid is True
        assert len(errors) == 0
        
        # Cleanup
        Path(temp_path).unlink()
    
    def test_validate_invalid_data(self):
        # Create invalid test data (missing required field)
        invalid_data = [
            {
                "instruction": "Test instruction",
                "input": "Test input"
                # Missing "output" field
            }
        ]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(invalid_data, f)
            temp_path = f.name
        
        is_valid, errors = validate_data_format(temp_path)
        
        assert is_valid is False
        assert len(errors) > 0
        assert "missing required field: output" in errors[0]
        
        # Cleanup
        Path(temp_path).unlink()
    
    def test_validate_nonexistent_file(self):
        is_valid, errors = validate_data_format("nonexistent_file.json")
        
        assert is_valid is False
        assert len(errors) == 1
        assert "not found" in errors[0]


class TestSampleDataCreation:
    def test_create_sample_data(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = Path(temp_dir) / "test_sample.json"
            
            result_path = create_sample_data(str(output_path), num_samples=5)
            
            assert Path(result_path).exists()
            
            # Validate created data
            with open(result_path, 'r') as f:
                data = json.load(f)
            
            assert len(data) == 5
            assert all("instruction" in item for item in data)
            assert all("input" in item for item in data)
            assert all("output" in item for item in data)


class TestUtilityFunctions:
    def test_format_memory_size(self):
        assert format_memory_size(1024) == "1.0 KB"
        assert format_memory_size(1024 * 1024) == "1.0 MB"
        assert format_memory_size(1024 * 1024 * 1024) == "1.0 GB"
    
    def test_get_model_size(self):
        assert get_model_size("model-7b") == "~7B parameters"
        assert get_model_size("Qwen3-4B-Thinking") == "~4B parameters"
        assert get_model_size("unknown-model") is None
