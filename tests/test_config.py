"""
Tests for configuration management
"""

import pytest
import tempfile
import yaml
from pathlib import Path

from medllm.config import (
    load_config, get_default_config, create_config_for_device,
    validate_config, merge_configs, get_config_template
)


class TestConfigLoading:
    def test_load_valid_config(self):
        config_data = {
            "model": {"name": "test-model"},
            "training": {"learning_rate": 0.001}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        loaded_config = load_config(temp_path)
        
        assert loaded_config == config_data
        
        # Cleanup
        Path(temp_path).unlink()
    
    def test_load_nonexistent_config(self):
        with pytest.raises(FileNotFoundError):
            load_config("nonexistent_config.yaml")


class TestDefaultConfig:
    def test_get_default_config_structure(self):
        config = get_default_config()
        
        required_sections = ["model", "training", "lora", "data", "rag", "wandb"]
        for section in required_sections:
            assert section in config
        
        # Check required model fields
        assert "name" in config["model"]
        assert config["model"]["name"] == "Qwen/Qwen3-4B-Thinking-2507"
        
        # Check required training fields
        assert "learning_rate" in config["training"]
        assert "output_dir" in config["training"]


class TestDeviceConfigs:
    def test_create_config_for_cuda(self):
        config = create_config_for_device("cuda")
        
        assert config["model"]["load_in_4bit"] is True
        assert config["training"]["bf16"] is True
        assert config["training"]["fp16"] is False
    
    def test_create_config_for_mps(self):
        config = create_config_for_device("mps")
        
        assert config["model"]["load_in_4bit"] is False
        assert config["model"]["device_map"] is None
        assert config["training"]["fp16"] is True
        assert config["training"]["bf16"] is False
    
    def test_create_config_for_cpu(self):
        config = create_config_for_device("cpu")
        
        assert config["model"]["load_in_4bit"] is False
        assert config["model"]["torch_dtype"] == "float32"
        assert config["training"]["bf16"] is False
        assert config["training"]["fp16"] is False


class TestConfigValidation:
    def test_validate_valid_config(self):
        config = get_default_config()
        
        # Should not raise any exception
        assert validate_config(config) is True
    
    def test_validate_missing_section(self):
        config = {"model": {"name": "test"}}  # Missing required sections
        
        with pytest.raises(ValueError, match="Missing required configuration section"):
            validate_config(config)
    
    def test_validate_missing_model_name(self):
        config = {
            "model": {},  # Missing name
            "training": {"learning_rate": 0.001},
            "lora": {"r": 16}
        }
        
        with pytest.raises(ValueError, match="Model name is required"):
            validate_config(config)


class TestConfigMerging:
    def test_merge_configs(self):
        base_config = {
            "model": {"name": "base-model", "param1": "value1"},
            "training": {"lr": 0.001}
        }
        
        override_config = {
            "model": {"name": "new-model", "param2": "value2"},
            "new_section": {"param": "value"}
        }
        
        merged = merge_configs(base_config, override_config)
        
        # Check that values are properly merged/overridden
        assert merged["model"]["name"] == "new-model"  # Overridden
        assert merged["model"]["param1"] == "value1"   # Preserved
        assert merged["model"]["param2"] == "value2"   # Added
        assert merged["training"]["lr"] == 0.001       # Preserved
        assert merged["new_section"]["param"] == "value"  # Added


class TestConfigTemplates:
    def test_get_quick_template(self):
        config = get_config_template("quick")
        
        assert config["training"]["num_train_epochs"] == 1
        assert config["training"]["max_steps"] == 100
    
    def test_get_full_template(self):
        config = get_config_template("full")
        
        assert config["training"]["num_train_epochs"] == 5
    
    def test_get_invalid_template(self):
        with pytest.raises(ValueError, match="Unknown template"):
            get_config_template("invalid_template")
